<template>
  <div class="py-2 bg-white relative overflow-hidden">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <div class="block py-4 bg-white overflow-x-auto transition-all duration-300 ease-in-out"
         :class="{'translate-x-0 opacity-100': !isBetSlipOpen, '-translate-x-full opacity-0 absolute inset-0': isBetSlipOpen}">
      <!-- Filters - Redesigned to be more compact -->
      <div class="px-2 pb-2 flex flex-wrap gap-2 w-full">
        <!-- Bet Ref -->
        <div class="w-48">
          <label class="block text-xs font-bold text-gray-700">Bet Ref</label>
          <input type="text" placeholder="Ref" @keyup.enter="applyFilters()"
                 class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                 v-model="moreParams.bet_reference">
        </div>

        <!-- Bet ID -->
        <div class="w-48">
          <label class="block text-xs font-bold text-gray-700">Bet ID</label>
          <input type="text" placeholder="ID" @keyup.enter="applyFilters()"
                 class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                 v-model="moreParams.bet_id">
        </div>

        <!-- Bet Amount -->
        <div class="w-48">
          <label class="block text-xs font-bold text-gray-700">Bet Amount</label>
          <div class="flex gap-1">
            <input type="number" placeholder="Min" @keyup.enter="applyFilters()"
                   class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.stake_amount_min">
            <input type="number" placeholder="Max" @keyup.enter="applyFilters()"
                   class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.stake_amount_max">
          </div>
        </div>

        <!-- Apply Button -->
        <div class="flex items-end">
          <button @click="applyFilters()"
                  class="px-3 py-1 bg-primary text-white text-xs rounded-md">
            Apply
          </button>
        </div>
      </div>

      <!-- Replace the table with AutoTable component -->
      <auto-table
        v-if="bets.length > 0"
        :headers="tableHeaders"
        :data="bets"
        :has-actions="true"
        :get-actions="getRowActions"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage"
        @items-per-page-change="handleLimitChange"
      >
        <!-- Bet ID Column -->
        <template #bet_id="{ item }">
          <div>{{ item.bet_id }}</div>
        </template>

        <!-- Bet Reference Column -->
        <template #bet_reference="{ item }">
          <div>{{ item.bet_reference }}</div>
        </template>

        <!-- Game Name Column -->
        <template #game_name="{ item }">
          <div class="game-badge" :class="getGameClass(getExtraDataValue(item, 'gameName'))">
            {{ getExtraDataValue(item, "gameName") }}
          </div>
        </template>

        <!-- Stake Column -->
        <template #bet_amount="{ item }">
          <div>{{ formatNumber(item.bet_amount) }}</div>
        </template>

        <!-- Possible Win Column -->
        <template #possible_win="{ item }">
          <div>
            <strong>{{ formatNumber(item.possible_win) }}</strong>
            <!-- <br>
            <span style="font-size: 11px;">W.Tax: {{ formatNumber(item.witholding_tax) }}</span> -->
          </div>
        </template>

        <!-- Bet Type Column -->
        <template #bet_type="{ item }">
          <div class="bet-type-badge" :class="getBetTypeClass(item.bet_type)">
            {{ betTypeText(item.bet_type) }}
          </div>
        </template>

        <!-- Total Odd Column -->
        <template #total_odd="{ item }">
          <div class="text-center">{{ formatNumber(item.total_odd) }}</div>
        </template>

        <!-- Status Column -->
        <template #status="{ item }">
          <div class="status-badge" :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </div>
        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <span style="font-size: 11px; color: grey">{{ moment(item.created_at).format('llll') }}</span>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item }">
          <div class="relative z-50">
            <action-dropdown 
              v-if="(parseInt(item.status) !== 0) && (parseInt(item.status) !== null)"
              button-text="Actions" 
              :show-text="false"
              button-class="z-50"
              menu-class="z-50 origin-top-right"
              :menu-width="48"
            >
              
              <action-item
                text="View Details"
                color="blue"
                @click="viewBetDetails(item)"
              >
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </template>
              </action-item>
            </action-dropdown>
          </div>
        </template>
      </auto-table>

      <div v-else class="py-8 text-center text-gray-500">
        No bet data available
      </div>
    </div>
    
    <!-- Bet Slip View (slides in from the right) -->
    <div class="block py-4 bg-white overflow-x-auto transition-all duration-300 ease-in-out"
         :class="{'translate-x-0 opacity-100': isBetSlipOpen, 'translate-x-full opacity-0 absolute inset-0': !isBetSlipOpen}">
      <div class="p-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">
            Bet Slip Details
            <span v-if="selectBet" class="text-sm font-normal text-gray-500 ml-2">
              ({{ selectBet.bet_reference || 'No Reference' }})
            </span>
          </h3>
          
          <button @click="closeBetSlip" class="text-gray-500 hover:text-gray-700 focus:outline-none">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div v-if="selectBet" class="mb-4 p-4 bg-gray-50 rounded-lg">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p class="text-sm text-gray-500">Bet Reference</p>
              <p class="font-bold">{{ selectBet.bet_reference }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">Stake</p>
              <p class="font-bold">{{ formatNumber(selectBet.bet_amount) }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">Possible Win</p>
              <p class="font-bold">{{ formatNumber(selectBet.possible_win) }}</p>
            </div>
          </div>
        </div>
        
        <auto-table
          v-if="bet_slips.length > 0"
          :headers="slipTableHeaders"
          :data="bet_slips"
          :total-items="modal_total"
          :items-per-page="modal_limit"
          :current-page-prop="modal_offset"
          :server-side-pagination="true"
          :pagination="modal_total > modal_limit"
          :show-items-count="true"
          :items-per-page-options="[10, 25, 50]"
          @page-change="handleModalPageChange"
          @items-per-page-change="handleModalLimitChange"
        >
          <!-- Slip ID Column -->
          <template #slip_id="{ item }">
            <div>{{ item.slip_id }}</div>
          </template>
          
          <!-- Teams Column -->
          <template #teams="{ item }">
            <div v-if="item.extra_data">
              <strong>{{ getExtraDataValues(item).competitor1 }}</strong>
              vs
              <strong>{{ getExtraDataValues(item).competitor2 }}</strong>
            </div>
          </template>

          <!-- Bet Type Column -->
          <template #live_bet="{ item }">
            <div class="live-bet-badge" :class="getLiveBetClass(item.live_bet)">
              {{ item.live_bet === "1" ? "Live" : "Pre-match" }}
            </div>
          </template>

          <!-- Pick Column -->
          <template #pick="{ item }">
            <strong>{{ item.pick }}</strong>
          </template>

          <!-- Odds Column -->
          <template #odd_value="{ item }">
            <div class="text-center">
              <span class="status-badge" :class="getOddsClass(item.odd_value)">
                {{ formatNumber(item.odd_value) }}
              </span>
            </div>
          </template>

          <!-- Winning Outcome Column -->
          <template #winning_outcome="{ item }">
            <span>{{ item.winning_outcome ? item.winning_outcome : "Not Resulted" }}</span>
          </template>

          <!-- Scores Column -->
          <template #scores="{ item }">
            <div>
              HT: {{ item.ht_scores ? item.ht_scores : " - : -" }}
              <br>
              FT: {{ item.ft_scores ? item.ft_scores : " - : -" }}
              <br>
              ET: {{ item.et_scores ? item.et_scores : " - : -" }}
            </div>
          </template>

          <!-- Status Column -->
          <template #status="{ item }">
            <div class="status-badge" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </div>
          </template>

          <!-- Start Time Column -->
          <template #start_time="{ item }">
            <span style="font-size: 11px; color: grey">{{ moment(item.start_time).format('llll') }}</span>
          </template>
        </auto-table>
        
        <div v-else class="py-8 text-center text-gray-500">
          No bet slip data available
        </div>
      </div>
    </div>
    
    <!-- Bet Details Modal -->
    <div v-if="isDetailsModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <!-- Enhanced Header with Bet Reference, MSISDN and IP -->
          <div class="border-b pb-4 mb-4">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-xl font-bold">Bet Details</h3>
                <div class="mt-1 text-sm text-gray-600 flex flex-col space-y-1">
                  <div class="flex items-center">
                    <span class="font-medium w-24">Ref:</span> 
                    <span 
                      class="badge reference-badge cursor-pointer" 
                      @click="copyToClipboard(detailsData.bet_reference)"
                      title="Click to copy"
                    >
                      {{ detailsData.bet_reference }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="font-medium w-24">MSISDN:</span> 
                    <span 
                      class="badge mobile-badge cursor-pointer" 
                      @click="copyToClipboard(getExtraDataValue(detailsData, 'msisdn'))"
                      title="Click to copy"
                    >
                      {{ getExtraDataValue(detailsData, "msisdn") }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="font-medium w-24">IP:</span> 
                    <span 
                      class="badge ip-badge cursor-pointer" 
                      @click="copyToClipboard(detailsData.ip_address || getExtraDataValue(detailsData, 'ip'))"
                      title="Click to copy"
                    >
                      {{ detailsData.ip_address || getExtraDataValue(detailsData, "ip") }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="font-medium w-24">Game:</span> 
                    <span 
                      class="badge game-badge cursor-pointer" 
                      :class="getGameClass(getExtraDataValue(detailsData, 'gameName'))"
                      @click="copyToClipboard(getExtraDataValue(detailsData, 'gameName') || detailsData.game_name)"
                      title="Click to copy"
                    >
                      {{ getExtraDataValue(detailsData, "gameName") || detailsData.game_name }}
                    </span>
                  </div>
                </div>
              </div>
              <button @click="isDetailsModalOpen = false" class="text-gray-500 hover:text-gray-700">
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          
          <div v-if="detailsData" class="space-y-4">
            <!-- Bet Information -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 bg-gray-50 p-4 rounded-lg">
              <div>
                <p class="text-sm text-gray-500">Bet ID</p>
                <p class="font-bold cursor-pointer" @click="copyToClipboard(detailsData.bet_id)" title="Click to copy">
                  {{ detailsData.bet_id }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Stake</p>
                <p class="font-bold cursor-pointer" @click="copyToClipboard(formatNumber(detailsData.bet_amount))" title="Click to copy">
                  {{ formatNumber(detailsData.bet_amount) }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Possible Win</p>
                <p class="font-bold cursor-pointer" @click="copyToClipboard(formatNumber(detailsData.possible_win))" title="Click to copy">
                  {{ formatNumber(detailsData.possible_win) }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Withholding Tax</p>
                <p class="font-bold cursor-pointer" @click="copyToClipboard(formatNumber(detailsData.witholding_tax))" title="Click to copy">
                  {{ formatNumber(detailsData.witholding_tax) }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Total Odd</p>
                <p class="font-bold cursor-pointer" @click="copyToClipboard(formatNumber(detailsData.total_odd))" title="Click to copy">
                  {{ formatNumber(detailsData.total_odd) }}
                </p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Bet Type</p>
                <div class="bet-type-badge" :class="getBetTypeClass(detailsData.bet_type)">
                  {{ betTypeText(detailsData.bet_type) }}
                </div>
              </div>
              <div>
                <p class="text-sm text-gray-500">Status</p>
                <div class="status-badge" :class="getStatusClass(detailsData.status)">
                  {{ getStatusText(detailsData.status) }}
                </div>
              </div>
              <div>
                <p class="text-sm text-gray-500">Date</p>
                <p class="font-bold text-xs cursor-pointer" @click="copyToClipboard(moment(detailsData.created_at).format('lll'))" title="Click to copy">
                  {{ moment(detailsData.created_at).format('lll') }}
                </p>
              </div>
            </div>
            
            <!-- Extra Data Section -->
            <div v-if="detailsData.extra_data" class="mt-4">
              <h4 class="font-semibold text-lg mb-2">Additional Information</h4>
              
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="grid grid-cols-1 gap-4">
                  <div v-for="(value, key) in parseExtraData(detailsData.extra_data)" :key="key" class="border-b border-gray-200 pb-2">
                    <div class="flex flex-col">
                      <div class="mb-1">
                        <span class="text-sm font-medium text-gray-700">{{ formatKey(key) }}</span>
                      </div>
                      
                      <!-- Handle different types of values -->
                      <div v-if="typeof value === 'object'" class="w-full">
                        <button 
                          @click="showJsonViewer(value, formatKey(key))" 
                          class="text-blue-500 text-sm hover:text-blue-700 flex items-center"
                        >
                          <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                          </svg>
                          View JSON Data
                        </button>
                      </div>
                      
                      <div v-else-if="isLongText(value)" class="w-full">
                        <div class="bg-gray-100 p-2 rounded text-sm">
                          <div v-if="expandedTexts[key]">
                            <pre class="whitespace-pre-wrap break-words text-xs">{{ value }}</pre>
                            <div class="flex justify-between mt-1">
                              <button @click="toggleFullText(key)" class="text-blue-500 text-xs">Show less</button>
                              <button @click="copyToClipboard(value)" class="text-blue-500 text-xs">Copy</button>
                            </div>
                          </div>
                          <div v-else>
                            <p class="text-xs">{{ truncateText(value) }}</p>
                            <div class="flex justify-between mt-1">
                              <button @click="toggleFullText(key)" class="text-blue-500 text-xs">Show more</button>
                              <button @click="copyToClipboard(value)" class="text-blue-500 text-xs">Copy</button>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div v-else class="w-full">
                        <p class="text-sm bg-gray-100 p-2 rounded cursor-pointer" @click="copyToClipboard(value)" title="Click to copy">
                          {{ value }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- JSON Viewer Modal -->
    <json-viewer-modal
      :show="jsonViewerOpen"
      :title="jsonViewerTitle"
      :json-data="jsonViewerData"
      @close="jsonViewerOpen = false"
    />
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import { endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths } from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import { AutoTable, CustomLoading, ActionDropdown, ActionItem } from '@/components/common';
import JsonViewerModal from '@/components/common/JsonViewerModal.vue';

export default {
  components: {
    VueDatePicker,
    AutoTable,
    CustomLoading,
    ActionDropdown,
    ActionItem,
    JsonViewerModal
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      isBetSlipOpen: false,
      showDropdown: [],
      isModalOpen: false,
      bet_slips: [],
      modal_total: 0,
      modal_limit: 10,
      modal_offset: 1,
      selectBet: {},
      bet_slip_params: {
        bet_id: '',
        sport_id: '',
        status: '',
        start: '',
        end: '',
        page: '',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
      },
      bets: [],
      phone:'',
      moreParams: {
        stake_amount_min: '',
        stake_amount_max: '',
        winning_amount_min: '',
        winning_amount_max: '',
        odds_min: '',
        odds_max: '',
        selections_min: '',
        selections_max: '',
        bet_type: '',
        selection_type: '',
        profile_id: '',
        mobile_number: this.$store.state.customer.msisdn || '',
        status: '',
        sort: '',
        start: '',
        end: '',
        page: '',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      // Table headers for main table
      tableHeaders: [
        { key: 'bet_id', label: 'Bet ID', align: 'center' },
        { key: 'bet_reference', label: 'Bet Reference', align: 'center' },
        // { key: 'customer', label: 'Customer', align: 'center' },
        { key: 'game_name', label: 'Game Name', align: 'center' },
        { key: 'bet_amount', label: 'Stake', align: 'center' },
        // { key: 'total_games', label: 'Total Games', align: 'center' },
        { key: 'total_odd', label: 'Total Odd', align: 'center' },
        { key: 'possible_win', label: 'Possible Win', align: 'center' },
        { key: 'bet_type', label: 'Bet Type', align: 'center' },
        { key: 'bet_status', label: 'Status', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'center' },
      ],

      // Table headers for modal table
      slipTableHeaders: [
        { key: 'slip_id', label: 'ID', align: 'center' },
        { key: 'teams', label: 'Teams', align: 'center' },
        { key: 'live_bet', label: 'Bet Type', align: 'center' },
        { key: 'pick', label: 'Pick', align: 'center' },
        { key: 'odd_value', label: 'Odds', align: 'center' },
        { key: 'winning_outcome', label: 'Winning Outcome', align: 'center' },
        { key: 'scores', label: 'Scores', align: 'center' },
        { key: 'status', label: 'Status', align: 'center' },
        { key: 'start_time', label: 'Start Time', align: 'center' },
      ],

      selected_game_type: {},
      game_types: [
        {name: 'sports', value: 'sports'},
        {name: 'Virtual', value: 'virtual'},
        {name: 'Instant', value: 'instant'},
        {name: 'Casino', value: 'casino'},
      ],

      selected_selection_type: {},
      selection_types: [
        {text: 'Single Bet', value: 1},
        {text: 'Multi Bet', value: 2},
      ],

      selected_bet_type: {},
      bet_types: [
        {text: 'Cash Bet', value: 0},
        {text: 'Bonus Bet', value: 1},
        {text: 'Free Bet', value: 2},
      ],

      selected_is_live: {},
      is_live_types: [
        {text: 'PreMatch', value: 1},
        {text: 'Live', value: 2},
      ],

      selected_bet_status: {},
      bet_statuses: [
        {text: 'Active', value: 1},
        {text: 'Won ', value: 2},
        {text: 'Lost ', value: 3},
        {text: 'Rejected ', value: 4},
        {text: 'Returned ', value: 5},
      ],

      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
      isDetailsModalOpen: false,
      detailsData: {},
      expandedTexts: {}, // To track which texts are expanded
      jsonViewerOpen: false,
      jsonViewerTitle: '',
      jsonViewerData: null,
    }
  },
  methods: {
    ...mapActions(["getVirtualBets", "getVirtualBetSlip", "repostDeposit", "toggleSideMenu",]),

    toggleSideM() {
      this.toggleSideMenu()
    },

    // Get row actions for AutoTable
    getRowActions(item) {
      if ((parseInt(item.match_status) !== 0) && (parseInt(item.match_status) !== null)) {
        return [
          {
            label: 'View Details',
            action: () => this.viewBetDetails(item),
            icon: 'fas fa-info-circle'
          }
        ];
      }
      return [];
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setBets(this.phone);
    },

    // Handle modal items per page change
    handleModalLimitChange(newLimit) {
      this.modal_limit = newLimit;
      this.modal_offset = 1;
      // Reload modal data with new limit
      this.viewBetDetails(this.currentBetItem);
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async selectDate() {
      let vm = this
      vm.moreParams.start = vm.formatDate(this.date[0])
      vm.moreParams.end = vm.formatDate(this.date[1])
      vm.moreParams.timestamp = Date.now()

      await vm.setBets(this.phone)
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination for main table
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setBets(this.phone)
    },

    // Pagination for modal table
    handleModalPageChange(page) {
      // Ensure page is a number
      this.modal_offset = parseInt(page)
      this.bet_slip_params.page = page.toString()
      this.viewBetSlip(this.selectBet)
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    applyFilters(filters) {
      this.moreParams.page = ''
      this.moreParams.offset = ''
      this.setBets(this.phone)
    },

    async setBets(num) {
      let app = this
      app.isLoading = true
      app.moreParams.mobile_number = num
      app.phone = num

      let response = await this.getVirtualBets(app.moreParams)

      app.bets = []
      app.total = 0
      if (response.status === 200) {
        app.bets = response.message.result

        if (response.message.record_count !== 0) {
          // Ensure total is a number
          app.total = parseInt(response.message.record_count)
        }

        app.showDropdown = []
        for (let i = 0; i < app.bets.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.bets = [];
        app.total = 0;
      }
      app.isLoading = false
    },

    async viewBetSlip(bet) {
      let app = this
      app.isLoading = true
      app.closeDropdown() // Close any open dropdowns

      const params = new URLSearchParams();
      app.bet_slip_params.bet_id = bet.bet_id
      app.selectBet = bet

      for (const key in app.bet_slip_params) {
        if (app.bet_slip_params.hasOwnProperty(key)) {
          params.append(key, app.bet_slip_params[key]);
        }
      }

      const queryString = params.toString();
      let response = await this.getBetSlip(queryString)

      if (response.status === 200) {
        app.bet_slips = response.message.result || []
        // Ensure modal_total is a number by using parseInt
        app.modal_total = parseInt(response.message.record_count || 0)

        app.showDropdown = []
        for (let i = 0; i < app.bet_slips.length; i++) {
          app.showDropdown.push(false)
        }
        
        // Make sure to set this flag to true AFTER data is loaded
        app.isBetSlipOpen = true
        console.log("Bet slips loaded:", app.bet_slips.length, "items")
      } else {
        app.bet_slips = [];
        app.modal_total = 0;
        app.isBetSlipOpen = true // Still open the panel even if no data
        console.log("No bet slips found or error occurred")
      }
      app.isLoading = false
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (no decimal places for counts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    getExtraDataValue(data, key) {
      if (!data.extra_data) return null; // Check if extra_data exists

      try {
        const extraData = JSON.parse(data.extra_data); // Parse JSON string
        return extraData[key] ?? null; // Return the requested key or null if not found
      } catch (error) {
        console.error("Error parsing extra_data:", error);
        return null;
      }
    },

    getStatusClass(status) {
      const statusMap = {
        '0': 'status-pending', // Pending - gray
        '1': 'status-won',     // Won - green
        '3': 'status-lost'     // Lost - orange
      };
      return statusMap[status] || 'status-default';
    },

    getStatusText(status) {
      console.log("dsaddsadsadasd: ", status)
      const statusMap = {
        '0': 'Pending',
        '1': 'Won',
        '3': 'Lost',
        '7': 'Voided',
        '9': 'Canceled'
      };
      return statusMap[status] || 'Unknown';
    },

    betTypeText(status) {
      if (status === "0") return "Cash Bet";
      if (status === "1") return "Bonus Bdet";
      if (status === "2") return "Free Bet";
    },

    processedText(status) {
      return "9"//this.getStatusText(status);
    },

    // Method to parse extra_data and return as an object
    parseExtraData(extraData) {
      try {
        return JSON.parse(extraData);  // Parse the extra_data JSON string into an object
      } catch (error) {
        console.error('Error parsing extra_data:', error);
        return {};  // Return an empty object if parsing fails
      }
    },
    
    // Method to extract and return specific values from parsed extra_data
    getExtraDataValues(item) {
      const parsedData = this.parseExtraData(item.extra_data);
      return parsedData; // You can return specific properties as needed, e.g., parsedData.competitor1, parsedData.odd_id
    },
    // Add this method for bet type styling
    getBetTypeClass(betType) {
      const typeMap = {
        '0': 'bet-type-cash',
        '1': 'bet-type-bonus',
        '2': 'bet-type-free'
      };

      return typeMap[betType] || 'bet-type-default';
    },
    // Add this method for game name styling
    getGameClass(gameName) {
      const gameMap = {
        'Virtual League': 'game-virtual-league',
        'Virtual Turbo League': 'game-virtual-turbo'
      };

      return gameMap[gameName] || 'game-default';
    },
    // Add this method for live bet styling
    getLiveBetClass(liveBet) {
      return liveBet === '1' ? 'live-bet-live' : 'live-bet-prematch';
    },
    // Method to view bet details
    viewBetDetails(item) {
      this.closeDropdown();
      this.detailsData = item;
      this.expandedTexts = {}; // Reset expanded texts
      this.isDetailsModalOpen = true;
    },
    // Format keys for display
    formatKey(key) {
      return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
    },
    // Check if text is long
    isLongText(text) {
      return typeof text === 'string' && text.length > 50;
    },
    // Truncate long text
    truncateText(text) {
      if (typeof text !== 'string') return JSON.stringify(text);
      return text.length > 50 ? text.substring(0, 50) + '...' : text;
    },
    // Toggle full text display
    toggleFullText(key) {
      this.$set(this.expandedTexts, key, !this.expandedTexts[key]);
    },
    // Copy text to clipboard
    copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
        // You could add a toast notification here
        console.log('Text copied to clipboard');
      }).catch(err => {
        console.error('Failed to copy text: ', err);
      });
    },
    // Parse browser details JSON
    parseBrowserDetails(browserDetails) {
      try {
        return JSON.parse(browserDetails);
      } catch (error) {
        console.error('Error parsing browser details:', error);
        return {};
      }
    },
    // Method to view bet slip
    // async viewBetSlip(item) {
    //   this.closeDropdown();
    //   this.selectBet = item;
    //   let response = await this.setBetSlips(item);
    //   if (response.status === 200) {
    //     this.isBetSlipOpen = true;
    //   }
    // },
    // Method to close dropdowns
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },
    // Add method to close bet slip view
    closeBetSlip() {
      this.isBetSlipOpen = false;
      // Clear data when closing
      setTimeout(() => {
        this.bet_slips = [];
        this.modal_total = 0;
      }, 300); // Clear after transition completes
    },
    showJsonViewer(data, title) {
      this.jsonViewerTitle = title;
      this.jsonViewerData = data;
      this.jsonViewerOpen = true;
    },
    // Method to get odds class for styling
    getOddsClass(odds) {
      const oddValue = parseFloat(odds);

      if (isNaN(oddValue)) return 'bg-gray-500';
      if (oddValue < 5.0) return 'bg-blue-400';
      if (oddValue < 10.0) return 'bg-green-400';
      if (oddValue < 20.0) return 'bg-yellow-500';
      if (oddValue < 50.0) return 'bg-orange-500';
      if (oddValue < 100.0) return 'bg-red-500';
      if (oddValue >= 100.0) return 'bg-purple-500';
      return 'bg-purple-600';
    },
  },
}
</script>

<style scoped>
/* Add any custom styles here */
/* Status badges */
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.status-won {
  background-color: #2ecc71; /* Green for Won */
}

.status-lost {
  background-color: #f39c12; /* Orange for Lost */
}

.status-pending {
  background-color: #95a5a6; /* Gray for Pending */
}

.status-default {
  background-color: #7f8c8d; /* Darker Gray for default */
}

/* Bet type badges */
.bet-type-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.bet-type-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.bet-type-cash {
  background-color: #27ae60; /* Green for Cash Bet */
}

.bet-type-bonus {
  background-color: #3498db; /* Blue for Bonus Bet */
}

.bet-type-free {
  background-color: #00b8d4; /* Cyan for Free Bet */
}

.bet-type-default {
  background-color: #7f8c8d; /* Gray for default */
}

/* Game badges */
.game-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.game-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.game-virtual-league {
  background-color: #00b8d4; /* Cyan for Virtual League */
}

.game-virtual-turbo {
  background-color: #009688; /* Teal for Virtual Turbo League */
}

.game-default {
  background-color: #7f8c8d; /* Gray for default */
}

/* Live bet badges */
.live-bet-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.live-bet-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.live-bet-live {
  background-color: #3498db; /* Blue for Live */
}

.live-bet-prematch {
  background-color: #95a5a6; /* Gray for Pre-match */
}

/* Badges */
.badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.reference-badge {
  background-color: #3498db; /* Blue for Bet Reference */
}

.mobile-badge {
  background-color: #27ae60; /* Green for MSISDN */
}

.ip-badge {
  background-color: #e74c3c; /* Red for IP */
}

.game-badge {
  background-color: #9b59b6; /* Purple for Game */
}
</style>
