
<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Bulk SMS" pageSubtitle="Send Message" />

    <!-- Quick Navigation -->
    <div class="mx-3 mb-3 flex justify-end">
      <router-link
        to="/app/scheduled-sms"
        class="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>View Scheduled SMS</span>
      </router-link>
    </div>

    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 my-2">
        <div class="block">
          <label class="font-semibold text-black mb-1 block">Recipients</label>
          <div class="flex items-center space-x-2">
            <label class="inline-flex items-center cursor-pointer">
              <input type="checkbox" :checked="form.send_to === 2" @change="form.send_to = form.send_to === 1 ? 2 : 1"
                class="sr-only peer">
              <div
                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600">
              </div>
              <span class="ms-3 text-sm font-medium text-gray-700">{{ form.send_to === 1 ? 'Individual' : 'Multiple Recipients' }}</span>
            </label>
          </div>
        </div>
      </div>

      <div v-if="form.send_to === 2" class="relative mt-5">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Profile Status Card -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors"
            :class="{ 'border-blue-500 shadow-md': filterParams.status === 2 }">
            <div class="space-y-3">
              <!-- Status Filter -->
              <div>
                <h4 class="text-xs font-medium text-gray-700 mb-2">Profile Status</h4>
                <div class="grid grid-cols-2 gap-2">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="profile_status" :value="1" v-model="filterParams.status"
                      class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">All Profiles</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="profile_status" :value="6" v-model="filterParams.status"
                      class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">Verified</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="profile_status" :value="2" v-model="filterParams.status"
                      @change="handleUnverifiedSelection" class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">Unverified</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Balance Status Card -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors"
            :class="{ 'opacity-50 pointer-events-none': filterParams.status === 2 }">
            <div class="space-y-3">
              <!-- Balance Status -->
              <div>
                <h4 class="text-xs font-medium text-gray-700 mb-2">Balance Status</h4>
                <div class="grid grid-cols-2 gap-2">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="balance_status" :value="''" v-model="filterParams.balance_status"
                      class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">All</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="balance_status" :value="0" v-model="filterParams.balance_status"
                      class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">Zero Balance</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="balance_status" :value="1" v-model="filterParams.balance_status"
                      class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">With Balance</span>
                  </label>
                </div>

                <!-- Balance Range (only shown when "With Balance" is selected) -->
                <div v-if="filterParams.balance_status === 1" class="mt-2">
                  <div class="flex space-x-2">
                    <div class="w-1/2">
                      <input type="number" v-model="filterParams.balance_min"
                        class="w-full px-2 py-1.5 text-xs border rounded-md" placeholder="Min Balance">
                    </div>
                    <div class="w-1/2">
                      <input type="number" v-model="filterParams.balance_max"
                        class="w-full px-2 py-1.5 text-xs border rounded-md" placeholder="Max Balance">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Bonus Status Card -->
          <!-- <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors" :class="{'opacity-50 pointer-events-none': filterParams.status === 2}">
            <div class="space-y-3">
              <div>
                <h4 class="text-xs font-medium text-gray-700 mb-2">Bonus Status</h4>
                <div class="grid grid-cols-2 gap-2">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio"
                           name="bonus_status"
                           :value="''"
                           v-model="filterParams.bonus_status"
                           class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">All</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio"
                           name="bonus_status"
                           :value="0"
                           v-model="filterParams.bonus_status"
                           class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">Zero Bonus</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio"
                           name="bonus_status"
                           :value="1"
                           v-model="filterParams.bonus_status"
                           class="form-radio h-3 w-3 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">With Bonus</span>
                  </label>
                </div>

                <div v-if="filterParams.bonus_status === 1" class="mt-2">
                  <div class="flex space-x-2">
                    <div class="w-1/2">
                      <input
                        type="number"
                        v-model="filterParams.bonus_min"
                        class="w-full px-2 py-1.5 text-xs border rounded-md"
                        placeholder="Min Bonus"
                      >
                    </div>
                    <div class="w-1/2">
                      <input
                        type="number"
                        v-model="filterParams.bonus_max"
                        class="w-full px-2 py-1.5 text-xs border rounded-md"
                        placeholder="Max Bonus"
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div> -->


          <!-- Depositor Type Card -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors"
            :class="{ 'opacity-50 pointer-events-none': filterParams.status === 2 }">
            <div class="space-y-3">
              <!-- Depositor Type Radio Buttons -->
              <div class="flex flex-col space-y-2">
                <h4 class="text-xs font-medium text-gray-700 mb-1">Depositor Type</h4>
                <div class="flex flex-col space-y-2">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="depositor_type" :value="''" v-model="filterParams.depositor_type"
                      class="form-radio h-4 w-4 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">None</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="depositor_type" :value="'3'" v-model="filterParams.depositor_type"
                      class="form-radio h-4 w-4 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">All depositors</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="depositor_type" :value="'0'" v-model="filterParams.depositor_type"
                      class="form-radio h-4 w-4 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">Non-depositors</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="depositor_type" :value="'1'" v-model="filterParams.depositor_type"
                      class="form-radio h-4 w-4 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">One-time depositors</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="depositor_type" :value="'2'" v-model="filterParams.depositor_type"
                      class="form-radio h-4 w-4 text-blue-600">
                    <span class="ms-2 text-xs font-medium text-gray-700">Multi-depositors</span>
                  </label>

                  <!-- Deposit Counts and Amount (only shown when "Multi-depositors" is selected) -->
                  <div v-if="filterParams.depositor_type === '2'" class="ml-6 mt-1 space-y-3">
                    <!-- Deposit Counts -->
                    <div>
                      <h4 class="text-xs font-medium text-gray-700 mb-1">Deposit Counts</h4>
                      <div class="flex space-x-2">
                        <div class="w-1/2">
                          <input type="number" v-model="filterParams.deposit_count_min"
                            class="w-full px-2 py-1.5 text-xs border rounded-md" placeholder="Min">
                        </div>
                        <div class="w-1/2">
                          <input type="number" v-model="filterParams.deposit_count_max"
                            class="w-full px-2 py-1.5 text-xs border rounded-md" placeholder="Max">
                        </div>
                      </div>
                    </div>

                    <!-- Deposit Amount -->
                    <div>
                      <h4 class="text-xs font-medium text-gray-700 mb-1">Deposit Amount</h4>
                      <div class="flex space-x-2">
                        <div class="w-1/2">
                          <input type="number" v-model="filterParams.deposit_amount_min"
                            class="w-full px-2 py-1.5 text-xs border rounded-md" placeholder="Min">
                        </div>
                        <div class="w-1/2">
                          <input type="number" v-model="filterParams.deposit_amount_max"
                            class="w-full px-2 py-1.5 text-xs border rounded-md" placeholder="Max">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Source and Date Card -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors"
            :class="{ 'border-green-500 shadow-md': filterParams.status === 2 }">
            <div class="space-y-3">
              <!-- Origin Filter -->
              <div>
                <h4 class="text-xs font-medium text-gray-700 mb-1">Select Source</h4>
                <select v-model="filterParams.origin" class="w-full px-2 py-1.5 text-xs border rounded-md">
                  <option value="">All</option>
                  <option v-for="origin in campaignOrigins" :key="origin.utm_source" :value="origin.utm_source">
                    {{ origin.utm_source }}
                  </option>
                </select>
              </div>

              <!-- Date Range -->
              <div>
                <h4 class="text-xs font-medium text-gray-700 mb-1">Date Range</h4>
                <VueDatePicker v-model="date" range :enable-time-picker="false" :format="'yyyy-MM-dd'"
                  :preset-ranges="presetRanges" placeholder="Select date range" class="w-full text-xs"
                  @update:model-value="selectDate" />
              </div>

              <div class="flex justify-between items-center">
                <button @click="resetFilters()"
                  class="px-3 py-1.5 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-xs">
                  Reset
                </button>
                <button @click="applyFilters()"
                  class="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-xs">
                  Apply Filters
                </button>
              </div>

            </div>
          </div>

          <!-- Filter Actions Card -->
          <!-- <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors" :class="{'border-green-500 shadow-md': filterParams.status === 2}">
            <div class="flex justify-between items-center">
              <button
                @click="resetFilters()"
                class="px-3 py-1.5 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-xs"
              >
                Reset
              </button>
              <button
                @click="applyFilters()"
                class="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-xs"
              >
                Apply Filters
              </button>
            </div>
          </div> -->
        </div>
      </div>

      <div v-else class="block mt-5">
        <label v-if="clientName === ''" class="font-semibold text-black mb-1 block">Search Customer <strong
            v-show="customerName"> </strong></label>
        <label v-else class="font-semibold text-black mb-1 block">Search Customer From {{ clientName }} <strong
            v-show="customerName"> </strong></label>
        <!---->
        <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchCustomer"
          @click="toggleCustomerSearchDropdown" @input="searchCustomer.length >= 9 ? searchCustomers() : null"
          :placeholder="customerSearchDropdownPlaceholder || 'Search by name or phone number'">

        <ul class="left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
          v-if="customerSearchDropdown && filteredCustomers.length > 0">
          <li v-for="item in filteredCustomers" :key="item.id"
            class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium" @click="setCustomerId(item)">
            {{ item.first_name }} {{ item.last_name }} - {{ item.msisdn }}
          </li>
        </ul>

        <ul class="left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
          v-if="customerSearchDropdown && searchCustomer.length >= 9 && filteredCustomers.length === 0">
          <li class="py-2 px-3 text-xs font-medium text-gray-500">
            No customers found matching "{{ searchCustomer }}"
          </li>
        </ul>

        <ul class="left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
          v-if="customerSearchDropdown && searchCustomer.length < 8">
          <li class="py-2 px-3 text-xs font-medium text-gray-500">
            Type at least 9 characters to search
          </li>
        </ul>

      </div>

      <!-- Campaign Details -->
      <!-- <div class="grid grid-cols-2 gap-4 mt-5">
        <div class="block">
          <label class="font-semibold text-black mb-1 block">Sender ID</label>
          <input
            type="text"
            v-model="bulk_params.sender_id"
            class="w-full px-3 py-2 border rounded-md outline-none"
            placeholder="Enter sender ID"
          >
        </div>
        <div class="block">
          <label class="font-semibold text-black mb-1 block">Campaign Name</label>
          <input
            type="text"
            v-model="bulk_params.campaign_name"
            class="w-full px-3 py-2 border rounded-md outline-none"
            placeholder="Enter campaign name"
          >
        </div>
      </div> -->

      <!-- Customer Count Display -->
      <div v-if="customerCount !== null" class="block mt-5 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-blue-800">Customer Count</h3>
            <p class="text-blue-600">{{ customerCountMessage }}</p>
          </div>
          <div class="text-right">
            <span class="text-2xl font-bold text-blue-800">{{ customerCount }}</span>
            <p class="text-sm text-blue-600">customers</p>
          </div>
        </div>
      </div>

      <div class="block mt-5">
        <label class="font-semibold text-black mb-1 block">Message to send</label>
        <div class="block border-2">
          <div class="ui top attached segment">
            <div class="ui fluid">
              <textarea class="p-2 text" v-model="bulk_params.message" name="w3review" rows="5"
                style="width: 100%; "></textarea>
            </div>
          </div>

          <div class="bg-blue-100">
            <div class="grid grid-cols-3 p-4">
              <div class="block"><strong><span v-text="characterCount"></span></strong> character(s)</div>
              <div class="block text-center"><strong><span v-text="wordCount"></span></strong> word(s)</div>
              <div class="block text-right"><strong><span v-text="pageCount"></span></strong> page(s)</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Scheduling Section -->
      <div class="block mt-5">
        <div class="flex items-center justify-between mb-3">
          <label class="font-semibold text-black">Schedule Message</label>
          <div class="flex items-center">
            <span class="text-sm text-gray-600 mr-2">Send Now</span>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="isScheduled" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
            <span class="text-sm text-gray-600 ml-2">Schedule</span>
          </div>
        </div>

        <!-- Enhanced Schedule Date/Time Picker -->
        <div v-if="isScheduled" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="mb-4">
            <h4 class="text-sm font-semibold text-blue-800 mb-3 flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              Schedule Settings
            </h4>
          </div>

          <!-- Date Section -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              📅 Select Date
            </label>
            <MobileFriendlyDatePicker
              v-model="scheduledDate"
              :enable-time-picker="false"
              :format="'yyyy-MM-dd'"
              :min-date="new Date()"
              placeholder="Select date"
              class="w-full"
            />
            <p class="text-xs text-gray-600 mt-1">Choose the date when the message should be sent</p>
          </div>

          <!-- Time Section -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              🕐 Select Time
            </label>
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Hour</label>
                <select v-model="scheduledHour" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option v-for="hour in hours" :key="hour" :value="hour">{{ hour.toString().padStart(2, '0') }}</option>
                </select>
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Minute</label>
                <select v-model="scheduledMinute" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option v-for="minute in minutes" :key="minute" :value="minute">{{ minute.toString().padStart(2, '0') }}</option>
                </select>
              </div>
            </div>
            <p class="text-xs text-gray-600 mt-1">Choose the time when the message should be sent</p>
          </div>

          <!-- Schedule Summary -->
          <div v-if="scheduledDate && scheduledHour !== null && scheduledMinute !== null" class="mt-4 p-3 bg-white border border-blue-200 rounded-md">
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span class="text-gray-700">
                <strong>Scheduled for:</strong> {{ formatScheduledSummary() }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Send Button - Only show when customerCount is 1 or more -->
      <button v-if="customerCount !== null && customerCount >= 1"
        class="w-full inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20 mt-10"
        @click="sendMessage" id="addMember">
        <span v-show="isLoading" class="text-red-600">Loading...</span>
        {{ isScheduled ? 'Schedule' : 'Send' }} to {{ customerCount }} customer{{ customerCount > 1 ? 's' : '' }}
        <span v-if="isScheduled && scheduledDateTime" class="block text-xs mt-1">
          Scheduled for: {{ formatScheduledDateTime(scheduledDateTime) }}
        </span>
      </button>

      <!-- Message when no customers found -->
      <div v-else-if="customerCount === 0" class="w-full text-center py-4 mt-10">
        <p class="text-gray-500">No customers match your criteria. Please adjust your filters.</p>
      </div>

    </div>

  </div>
</template>


<script>
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import { MobileFriendlyDatePicker } from '@/components/common';
import { mapActions } from "vuex";
import moment from "moment-timezone";
import VueDatePicker from "@vuepic/vue-datepicker";
import '@vuepic/vue-datepicker/dist/main.css';
import { el } from 'date-fns/locale';

export default {
  components: {
    CustomLoading,
    PageHeader,
    VueDatePicker,
    MobileFriendlyDatePicker
  },
  data() {
    return {
      text: "",
      characterCount: 0,
      wordCount: 0,
      pageCount: 0,

      // Date picker
      date: null,

      // Preset date ranges for date picker
      presetRanges: [
        { label: 'Today', range: [new Date(), new Date()] },
        { label: 'Last 7 Days', range: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'Last 30 Days', range: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'This Month', range: [new Date(new Date().setDate(1)), new Date()] },
        {
          label: 'Last Month', range: [
            new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
            new Date(new Date().getFullYear(), new Date().getMonth(), 0)
          ]
        }
      ],

      // Filter parameters
      filterParams: {
        status: 1, // 1-all profiles (default), 6-active, 2-unverified
        balance_status: '', // ''-all (default), 0-zero balance, 1-with balance
        balance_min: '', // Min balance amount when balance_status is 1
        balance_max: '', // Max balance amount when balance_status is 1
        bonus_status: '', // ''-all (default), 0-zero bonus, 1-with bonus
        bonus_min: '', // Min bonus amount when bonus_status is 1
        bonus_max: '', // Max bonus amount when bonus_status is 1
        start_date: '',
        end_date: '',
        deposit_count_min: '',
        deposit_count_max: '',
        deposit_amount_min: '',
        deposit_amount_max: '',
        depositor_type: '', // '3'-all depositors (default), '0'-non-depositors, '1'-one-time, '2'-multi-depositors
        origin: '',
        blast_filter: '' // Blast filter for predefined user groups - exact list from backend
      },

      // Campaign origins for filter
      campaignOrigins: [],

      // Blast filter options - exact list from backend
      blastFilterOptions: [
        { value: 'all', name: 'all' },
        { value: 'test', name: 'test' },
        { value: 'all_depositors', name: 'all_depositors' },
        { value: 'non_depositors', name: 'non_depositors' },
        { value: 'non_depositors_1', name: 'non_depositors_1' },
        { value: 'non_depositors_2', name: 'non_depositors_2' },
        { value: 'non_depositors_3', name: 'non_depositors_3' },
        { value: 'non_depositors_4', name: 'non_depositors_4' },
        { value: 'non_depositors_5', name: 'non_depositors_5' },
        { value: 'non_depositors_6', name: 'non_depositors_6' }
      ],

      // search
      selectedFilters: [],
      filteredFilters: [],
      filters: [],
      searchFilter: "",
      filterSearchDropdown: false,
      filterSearchDropdownPlaceholder: "Search and Select Filter",
      filterName: "",

      ///
      client_id: "",
      organisations: [],
      //
      customers: [],
      customerName: "",
      customerId: "",
      searchCustomer: "",
      customerSearchDropdownPlaceholder: "Search by name or phone number",
      customerSearchDropdown: false,
      filteredCustomers: [],

      ///
      isLoading: false,
      fullPage: true,

      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",

      moreParamsCustomer: {
        start: "",
        end: "",
        client_id: '',
        limit: 1000
      },

      /// old
      form: {
        send_to: 2,
        dial_code: "254",
        company_phone: null,
        company_address: null,
        company_name: null,
        company_email: null,
        contact_person: null
      },

      sendTo: [
        { text: 'To Individual', value: 1 },
        { text: 'To Many', value: 2 },
      ],

      bulk_params: {
        timestamp: '',
        message: '',
        sender_id: '',
        campaign_name: '',
      },

      // Customer count display
      customerCount: null,
      customerCountMessage: '',

      // Scheduling functionality
      isScheduled: false,
      scheduledDateTime: null,
      scheduledDate: null,
      scheduledHour: null,
      scheduledMinute: null,
      isUpdatingFromComponents: false,

    }
  },
  watch: {
    searchFilter(newVal, oldVal) {
      if (newVal !== oldVal && newVal !== "") {
        this.filteredFilters();
      }
    },

    'bulk_params.message': function (val, oldVal) {
      if (val !== oldVal) {
        // Count characters
        const charCount = val.length;
        this.characterCount = charCount;

        // Count words (split by whitespace and filter out empty strings)
        const wordCount = val.trim() ? val.trim().split(/\s+/).length : 0;
        this.wordCount = wordCount;

        // Calculate pages (160 characters per SMS page)
        const pageCount = Math.ceil(charCount / 160) || 0;
        this.pageCount = pageCount;
      }
    },

    // Watch for profile status changes
    'filterParams.status': function (newVal) {
      // If status is changed to Unverified (3), handleUnverifiedSelection will be called via the @change event
      // For other statuses, we don't need to do anything special
    },

    // Watch for balance status changes
    'filterParams.balance_status': function (newVal) {
      // Clear balance min/max when "all" (empty string) or "zero balance" (0) is selected
      if (newVal === '' || newVal === 0) {
        this.filterParams.balance_min = '';
        this.filterParams.balance_max = '';
      }
    },

    // Watch for bonus status changes
    'filterParams.bonus_status': function (newVal) {
      // Clear bonus min/max when "all" (empty string) or "zero bonus" (0) is selected
      if (newVal === '' || newVal === 0) {
        this.filterParams.bonus_min = '';
        this.filterParams.bonus_max = '';
      }
    },
  },
  computed: {
    hours() {
      return Array.from({ length: 24 }, (_, i) => i);
    },
    minutes() {
      return Array.from({ length: 60 }, (_, i) => i);
    }
  },
  watch: {
    // Watch for changes in date/time components and update scheduledDateTime
    scheduledDate() {
      this.updateScheduledDateTime();
    },
    scheduledHour() {
      this.updateScheduledDateTime();
    },
    scheduledMinute() {
      this.updateScheduledDateTime();
    },
    // Watch for changes in scheduledDateTime and update components
    scheduledDateTime(newVal) {
      if (newVal && !this.isUpdatingFromComponents) {
        this.updateDateTimeComponents(newVal);
      }
    }
  },
  async mounted() {
    await this.fetchCampaignOrigins()
  },
  methods: {
    ...mapActions(["toggleSideMenu", "getCampaignOrigins", "getProfiles", "sendBulkSMS"]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    // Date picker methods
    selectDate() {
      if (this.date && Array.isArray(this.date) && this.date.length === 2) {
        // Format dates as YYYY-MM-DD
        this.filterParams.start_date = this.formatDate(this.date[0]);
        this.filterParams.end_date = this.formatDate(this.date[1]);
      }
    },

    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    formatDateTime(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const seconds = String(d.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    formatScheduledDateTime(dateTime) {
      if (!dateTime) return '';
      const d = new Date(dateTime);
      return d.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    },

    // Enhanced scheduling methods
    updateScheduledDateTime() {
      if (this.scheduledDate && this.scheduledHour !== null && this.scheduledMinute !== null) {
        this.isUpdatingFromComponents = true;
        const date = new Date(this.scheduledDate);
        date.setHours(this.scheduledHour, this.scheduledMinute, 0, 0);
        this.scheduledDateTime = date;
        this.$nextTick(() => {
          this.isUpdatingFromComponents = false;
        });
      }
    },

    updateDateTimeComponents(dateTime) {
      if (dateTime) {
        const date = new Date(dateTime);
        this.scheduledDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        this.scheduledHour = date.getHours();
        this.scheduledMinute = date.getMinutes();
      }
    },

    formatScheduledSummary() {
      if (!this.scheduledDate || this.scheduledHour === null || this.scheduledMinute === null) {
        return '';
      }

      const date = new Date(this.scheduledDate);
      date.setHours(this.scheduledHour, this.scheduledMinute, 0, 0);

      return date.toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    },

    // Filter methods
    async applyFilters() {
      // Create filter tags based on selected filter parameters
      this.selectedFilters = [];

      // Add profile status filter
      if (this.filterParams.status !== 1) {
        let statusText = '';
        switch (this.filterParams.status) {
          case 2:
            statusText = 'Active';
            break;
          case 3:
            statusText = 'Unverified';
            break;
          default:
            statusText = 'Unknown';
        }
        this.selectedFilters.push({
          text: `Profile Status: ${statusText}`,
          value: `status_${this.filterParams.status}`
        });
      }

      // Add balance status filter
      if (this.filterParams.balance_status !== 0) {
        let balanceText = '';
        switch (this.filterParams.balance_status) {
          case 4:
            balanceText = 'Zero Balance';
            break;
          case 5:
            // If min/max values are provided, include them in the display text
            if (this.filterParams.balance_min || this.filterParams.balance_max) {
              const minText = this.filterParams.balance_min || 'Any';
              const maxText = this.filterParams.balance_max || 'Any';
              balanceText = `Balance: ${minText} to ${maxText}`;
            } else {
              balanceText = 'With Balance';
            }
            break;
          default:
            balanceText = 'Unknown';
        }

        // Create the filter value with min/max if applicable
        let filterValue = `balance_${this.filterParams.balance_status}`;
        if (this.filterParams.balance_status === 5 && (this.filterParams.balance_min || this.filterParams.balance_max)) {
          const minVal = this.filterParams.balance_min || '0';
          const maxVal = this.filterParams.balance_max || 'max';
          filterValue = `balance_${this.filterParams.balance_status}_${minVal}_${maxVal}`;
        }

        this.selectedFilters.push({
          text: `Balance: ${balanceText}`,
          value: filterValue
        });
      }

      // Add bonus status filter
      if (this.filterParams.bonus_status !== 0) {
        let bonusText = '';
        switch (this.filterParams.bonus_status) {
          case 6:
            bonusText = 'Zero Bonus';
            break;
          case 7:
            // If min/max values are provided, include them in the display text
            if (this.filterParams.bonus_min || this.filterParams.bonus_max) {
              const minText = this.filterParams.bonus_min || 'Any';
              const maxText = this.filterParams.bonus_max || 'Any';
              bonusText = `Bonus: ${minText} to ${maxText}`;
            } else {
              bonusText = 'With Bonus';
            }
            break;
          default:
            bonusText = 'Unknown';
        }

        // Create the filter value with min/max if applicable
        let filterValue = `bonus_${this.filterParams.bonus_status}`;
        if (this.filterParams.bonus_status === 7 && (this.filterParams.bonus_min || this.filterParams.bonus_max)) {
          const minVal = this.filterParams.bonus_min || '0';
          const maxVal = this.filterParams.bonus_max || 'max';
          filterValue = `bonus_${this.filterParams.bonus_status}_${minVal}_${maxVal}`;
        }

        this.selectedFilters.push({
          text: `Bonus: ${bonusText}`,
          value: filterValue
        });
      }

      // Add date range filter
      if (this.filterParams.start_date && this.filterParams.end_date) {
        this.selectedFilters.push({
          text: `Date: ${this.filterParams.start_date} to ${this.filterParams.end_date}`,
          value: `date_${this.filterParams.start_date}_${this.filterParams.end_date}`
        });
      }

      // Add deposit count filter
      if (this.filterParams.deposit_count_min || this.filterParams.deposit_count_max) {
        const minText = this.filterParams.deposit_count_min || 'Any';
        const maxText = this.filterParams.deposit_count_max || 'Any';
        this.selectedFilters.push({
          text: `Deposit Count: ${minText} to ${maxText}`,
          value: `deposit_count_${minText}_${maxText}`
        });
      }

      // Add deposit amount filter
      if (this.filterParams.deposit_amount_min || this.filterParams.deposit_amount_max) {
        const minText = this.filterParams.deposit_amount_min || 'Any';
        const maxText = this.filterParams.deposit_amount_max || 'Any';
        this.selectedFilters.push({
          text: `Deposit Amount: ${minText} to ${maxText}`,
          value: `deposit_amount_${minText}_${maxText}`
        });
      }

      // Add depositor type filter
      if (this.filterParams.depositor_type === '0') {
        this.selectedFilters.push({
          text: 'Non-depositors',
          value: 'non_depositors'
        });
      } else if (this.filterParams.depositor_type === '1') {
        this.selectedFilters.push({
          text: 'One-time depositors',
          value: 'one_time_depositors'
        });
      } else if (this.filterParams.depositor_type === '2') {
        this.selectedFilters.push({
          text: 'Multi-depositors',
          value: 'multi_depositors'
        });
      }

      // Add origin filter
      if (this.filterParams.origin) {
        this.selectedFilters.push({
          text: `Origin: ${this.filterParams.origin}`,
          value: `origin_${this.filterParams.origin}`
        });
      }

      // Add blast filter
      if (this.filterParams.blast_filter) {
        const selectedOption = this.blastFilterOptions.find(option => option.value === this.filterParams.blast_filter);
        const displayName = selectedOption ? selectedOption.name : this.filterParams.blast_filter;

        this.selectedFilters.push({
          text: `Blast Filter: ${displayName}`,
          value: `blast_filter_${this.filterParams.blast_filter}`
        });
      }

      // Update bulk_params.filter with the selected filters
      this.updateBulkParamsFilter();

      // Get customer count using the same function as sendMessage
      await this.getCustomerCount();
    },

    async getCustomerCount() {
      try {
        this.isLoading = true;

        // Create payload with count: true
        const payload = this.createMessagePayload(true);

        // Call the same API endpoint with count parameter
        const response = await this.sendBulkSMS(payload);
        console.log("response: dfsdkfsdf" + JSON.stringify(response));

        // Parse the response structure based on your example
        if (response.status === 200) {
          this.customerCount = response.message.result;
          this.customerCountMessage = response.message.message;
        } else {
          this.customerCount = null;
          this.customerCountMessage = '';
        }

      } catch (error) {
        console.error('Error getting customer count:', error);
        this.customerCount = null;
        this.customerCountMessage = '';
        this.$toast.error('Error getting customer count');
      } finally {
        this.isLoading = false;
      }
    },

    resetFilters() {
      this.filterParams = {
        status: 1, // 1-all profiles (default), 6-active, 2-unverified
        balance_status: '', // ''-all (default), 0-zero balance, 1-with balance
        balance_min: '', // Min balance amount when balance_status is 1
        balance_max: '', // Max balance amount when balance_status is 1
        bonus_status: '', // ''-all (default), 0-zero bonus, 1-with bonus
        bonus_min: '', // Min bonus amount when bonus_status is 1
        bonus_max: '', // Max bonus amount when bonus_status is 1
        start_date: '',
        end_date: '',
        deposit_count_min: '',
        deposit_count_max: '',
        deposit_amount_min: '',
        deposit_amount_max: '',
        depositor_type: '3', // '3'-all depositors (default), '0'-non-depositors, '1'-one-time, '2'-multi-depositors
        origin: '',
        blast_filter: '' // Blast filter for predefined user groups - exact list from backend
      };
      this.date = null;
      this.selectedFilters = [];
      this.customerCount = null;
      this.customerCountMessage = '';

      // Reset scheduling state
      this.isScheduled = false;
      this.scheduledDateTime = null;
      this.scheduledDate = null;
      this.scheduledHour = null;
      this.scheduledMinute = null;

      this.updateBulkParamsFilter();
    },

    updateBulkParamsFilter() {
      // Convert selected filters to a format suitable for the API
      const filterString = this.selectedFilters.map(filter => filter.value).join(',');
      this.bulk_params.filter = filterString;
    },

    toggleFilters(item) {
      this.toggleFiltersSearchDropdown()
      const index = this.selectedFilters.findIndex(org => org.value === item.value);
      if (index === -1) {
        this.selectedFilters.push(item);
      } else {
        this.selectedFilters.splice(index, 1);
      }
      this.updateBulkParamsFilter();
    },

    removeFilters(index) {
      this.selectedFilters.splice(index, 1);
      this.updateBulkParamsFilter();
    },

    toggleFiltersSearchDropdown() {
      // Toggle the value of filterSearchDropdown
      this.filterSearchDropdown = !this.filterSearchDropdown;
    },

    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
      this.customerSearchDropdown = false;
      this.searchCustomer = ""
      this.customerName = ""
      this.customerSearchDropdownPlaceholder = ""
    },

    toggleCustomerSearchDropdown() {
      // Toggle the value of searchDropdown
      this.customerSearchDropdown = !this.customerSearchDropdown;

      if (this.customerSearchDropdown && this.searchCustomer.length > 8) {
        this.searchCustomers();
      }
    },

    // Handle Unverified profile status selection
    handleUnverifiedSelection() {
      if (this.filterParams.status === 2) {
        // Set all other filters to none/inactive except source and date
        this.filterParams.depositor_type = '3';
        this.filterParams.balance_status = 0; // Zero Balance
        this.filterParams.bonus_status = 0; // Zero Bonus
        this.filterParams.balance_min = '';
        this.filterParams.balance_max = '';
        this.filterParams.bonus_min = '';
        this.filterParams.bonus_max = '';
        this.filterParams.deposit_count_min = '';
        this.filterParams.deposit_count_max = '';
        this.filterParams.deposit_amount_min = '';
        this.filterParams.deposit_amount_max = '';
        this.filterParams.blast_filter = ''; // Reset blast filter

        // Keep source and date as they are
        // this.filterParams.origin remains unchanged
        // this.filterParams.start_date remains unchanged
        // this.filterParams.end_date remains unchanged

        // Add a message to inform the user
        // this.$swal.fire({
        //   title: 'Unverified Profile Selected',
        //   text: 'Other filters have been disabled. Only Source and Date filters are available with Unverified profiles.',
        //   icon: 'info',
        //   confirmButtonText: 'OK'
        // });
      }
    },

    // Fetch campaign origins with UTM sources
    async fetchCampaignOrigins() {
      try {
        this.isLoading = true;
        const response = await this.getCampaignOrigins({
          timestamp: Date.now(),
          limit: 100,
          page: 1
        });

        if (response.status === 200 && response.message) {
          // Filter only campaigns that have utm_source
          this.campaignOrigins = Array.isArray(response.message)
            ? response.message.filter(campaign => campaign.utm_source)
            : (response.message.result && Array.isArray(response.message.result))
              ? response.message.result.filter(campaign => campaign.utm_source)
              : [];
        }
      } catch (error) {
        console.error("Error fetching campaign origins:", error);
      } finally {
        this.isLoading = false;
      }
    },

    // Search customers using the profiles API
    async searchCustomers() {
      if (this.searchCustomer.length < 3) return;

      try {
        this.isLoading = true;
        const response = await this.getProfiles({
          mobile_number: this.searchCustomer,
          timestamp: Date.now(),
          limit: 1,
        });

        if (response.status === 200 && response.message) {
          this.customers = response.message;
          this.filteredCustomers = response.message;
        }
      } catch (error) {
        console.error("Error searching customers:", error);
      } finally {
        this.isLoading = false;
      }
    },

    // Set customer ID when selected from dropdown
    setCustomerId(customer) {
      this.customerId = customer.id;
      this.customerName = `${customer.first_name} ${customer.last_name}`;
      this.bulk_params.account_number = customer.msisdn;
      this.customerSearchDropdown = false;
    },

    //
    createMessagePayload(isCountRequest = false) {
      // Create a comprehensive payload with all filter parameters
      const payload = {
        // Required backend fields with defaults
        timestamp: Date.now(),
        sender_id: this.bulk_params.sender_id || 'MOSSBETS',
        campaign_name: this.bulk_params.campaign_name || 'MOSSBETS',
        blast_filter: this.filterParams.blast_filter || 'test',
        blast_message: this.bulk_params.message || 'This is a Test Message',

        // Additional fields
        message: this.bulk_params.message,
        account_number: this.bulk_params.account_number || '',

        // Depositor Type with min/max count and amount
        depositor_type: this.filterParams.depositor_type,
        deposit_count_min: this.filterParams.deposit_count_min,
        deposit_count_max: this.filterParams.deposit_count_max,
        deposit_amount_min: this.filterParams.deposit_amount_min,
        deposit_amount_max: this.filterParams.deposit_amount_max,

        // Profile Status
        profile_status: this.filterParams.status,

        // Balance Status with min/max
        balance_status: this.filterParams.balance_status,
        balance_min: this.filterParams.balance_min,
        balance_max: this.filterParams.balance_max,

        // Bonus Status with min/max
        bonus_status: this.filterParams.bonus_status,
        bonus_min: this.filterParams.bonus_min,
        bonus_max: this.filterParams.bonus_max,

        // Date Range
        start_date: this.filterParams.start_date,
        end_date: this.filterParams.end_date,

        // Source/Origin
        origin: this.filterParams.origin,

        // Count parameter - true for getting count, false/undefined for sending message
        count: isCountRequest
      };

      // Add scheduling information if scheduled
      if (this.isScheduled && this.scheduledDateTime && !isCountRequest) {
        const scheduledDate = new Date(this.scheduledDateTime);
        
        // Format date as YYYY-MM-DD
        const year = scheduledDate.getFullYear();
        const month = String(scheduledDate.getMonth() + 1).padStart(2, '0');
        const day = String(scheduledDate.getDate()).padStart(2, '0');
        const formattedDate = `${year}-${month}-${day}`;
        
        // Format time as HH:MM
        const hours = String(scheduledDate.getHours()).padStart(2, '0');
        const minutes = String(scheduledDate.getMinutes()).padStart(2, '0');
        const formattedTime = `${hours}:${minutes}`;
        
        // Add the required scheduling fields
        payload.isScheduled = 1; // 0 for scheduled, 1 for immediate
        payload.scheduleDate = formattedDate;
        payload.scheduleTime = formattedTime;
        
      } else if (!isCountRequest) {
        // When not scheduled, set isScheduled to 1 (immediate)
        payload.isScheduled = 0;
      }

      return payload;
    },

    // Augment requires JCEF (Chromium Embedded Framework) to display its interface.
// To enable JCEF support:
// Go to Help → Find Action (or press Ctrl+Shift+A / Cmd+Shift+A)
// Type "Choose Boot Java Runtime" and select this action
// Select a JBR with JCEF support from the list (marked with "jcef")
// Restart IntelliJ IDEA when prompted
// If no JBR with JCEF is available in the list, you may need to download a newer version of IntelliJ IDEA


    async sendMessage() {
      let app = this
      this.isLoading = true

      // Validate scheduling if enabled
      if (this.isScheduled) {
        if (!this.scheduledDate || this.scheduledHour === null || this.scheduledMinute === null) {
          this.$swal.fire('Error!', 'Please select both date and time for scheduling', 'error');
          this.isLoading = false;
          return;
        }

        // Ensure scheduledDateTime is updated
        this.updateScheduledDateTime();

        if (!this.scheduledDateTime) {
          this.$swal.fire('Error!', 'Please select a valid date and time for scheduling', 'error');
          this.isLoading = false;
          return;
        }

        const scheduledTime = new Date(this.scheduledDateTime);
        const currentTime = new Date();

        if (scheduledTime <= currentTime) {
          this.$swal.fire('Error!', 'Scheduled time must be in the future', 'error');
          this.isLoading = false;
          return;
        }
      }

      // Create payload for sending message (count: false)
      const payload = this.createMessagePayload(false);

      console.log("payload : " + JSON.stringify(payload));

      const actionText = this.isScheduled ? 'schedule' : 'send';
      const confirmText = this.isScheduled ? 'Yes, schedule!' : 'Yes, send!';
      const successText = this.isScheduled ? 'Message Scheduled!' : 'Message Sent!';
      const warningText = this.isScheduled
        ? `Doing this schedules SMS for ${this.formatScheduledDateTime(this.scheduledDateTime)}!`
        : "Doing this sends SMS to Customer(s)!";

      app.$swal.fire({
        title: 'Are you sure?',
        text: warningText,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: confirmText,
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          console.log("bulk_params : " + JSON.stringify(payload));
          // Always use the same API endpoint (sendBulkSMS) but with scheduling parameters
          return await this.sendBulkSMS(payload);
        },
      })
        .then(async (result) => {
          console.log("result: " + JSON.stringify(result))
          if (result.value.status === 200) {
            app.$swal.fire(successText, result.value.message, 'success')

          } else {
            app.$swal.fire('Error!', result.value.message, 'error')
          }
        })

      this.isLoading = false
    },


  },
}
</script>
